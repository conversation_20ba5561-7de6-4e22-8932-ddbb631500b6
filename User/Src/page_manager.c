//
// Created by <PERSON><PERSON> on 2025/8/26 17:38.
//

#include "page_manager.h"

page_manager_t PageManager;

/**
 * @brief  创建页面管理器
 * @param  factory: 页面工厂指针
 * @retval 页面管理器指针，失败返回NULL
 */
void pm_create(pm_page_factory_t *factory) {
    /* 初始化成员变量 */
    memset(&PageManager, 0, sizeof(page_manager_t));
    PageManager.factory = factory;
    PageManager.page_prev = NULL;
    PageManager.page_current = NULL;
    PageManager.root_default_style = NULL;

    /* 设置默认全局动画 */
    // pm_page_manager_set_global_load_anim_type(
    //         manager,
    //         PM_LOAD_ANIM_OVER_LEFT,
    //         PM_ANIM_TIME_DEFAULT,
    //         PM_ANIM_PATH_DEFAULT
    //         );

    PM_LOG_INFO("Page manager created");
}

/**
 * @brief  在页面池中搜索页面
 * @param  manager: 页面管理器指针
 * @param  name: 页面名称
 * @retval 页面基类指针，如果未找到则返回NULL
 */
static pm_page_base_t *pm_find_page_in_pool(page_manager_t *manager, const char *name) {
    if (manager == NULL || name == NULL) {
        return NULL;
    }

    for (size_t i = 0; i < manager->pool_size; i++) {
        if (strcmp(name, manager->page_pool[i]->name) == 0) {
            return manager->page_pool[i];
        }
    }
    return NULL;
}

/**
 * @brief  创建页面
 * @param  factory: 页面工厂指针
 * @param  name: 页面类名
 * @retval 页面基类指针，失败返回NULL
 */
pm_page_base_t *pm_page_factory_create_page(pm_page_factory_t *factory, const char *name) {
    if (factory == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return NULL;
    }

    if (factory->create_page == NULL) {
        PM_LOG_ERROR("Create function is not set");
        return NULL;
    }

    pm_page_base_t *page = factory->create_page(name);
    if (page == NULL) {
        PM_LOG_ERROR("Failed to create page: %s", name);
        return NULL;
    }

    PM_LOG_INFO("Page(%s) created by factory", name);
    return page;
}

/**
 * @brief  将页面注册到页面池
 * @param  manager: 页面管理器指针
 * @param  base: 页面基类指针
 * @param  name: 页面应用名称，不允许重复注册
 * @retval 注册成功返回true
 */
bool pm_register(page_manager_t *manager, pm_page_base_t *base, const char *name) {
    if (manager == NULL || base == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    if (manager->pool_size >= PM_MAX_PAGE_POOL_SIZE) {
        PM_LOG_ERROR("Page pool is full");
        return false;
    }

    if (pm_find_page_in_pool(manager, name) != NULL) {
        PM_LOG_ERROR("Page(%s) was multi registered", name);
        return false;
    }

    base->manager = manager;
    strncpy(base->name, name, PM_MAX_PAGE_NAME_LEN - 1);
    base->name[PM_MAX_PAGE_NAME_LEN - 1] = '\0';

    manager->page_pool[manager->pool_size] = base;
    manager->pool_size++;

    PM_LOG_INFO("Page(%s) registered", name);
    return true;
}

/**
 * @brief  安装页面，并将页面注册到页面池
 * @param  class_name: 页面的类名
 * @retval 成功返回true
 */
bool pm_install(const char *class_name) {
    if (class_name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }

    if (PageManager.factory == NULL) {
        PM_LOG_ERROR("Factory was not registered, can't install page");
        return false;
    }

    if (pm_find_page_in_pool(&PageManager, class_name) != NULL) {
        PM_LOG_ERROR("Page(%s) was registered", class_name);
        return false;
    }

    pm_page_base_t *base = pm_page_factory_create_page(PageManager.factory, class_name);
    if (base == NULL) {
        PM_LOG_ERROR("Factory has not %s", class_name);
        return false;
    }

    if (!pm_register(&PageManager, base, class_name)) {
        PM_LOG_ERROR("Page(%s) register failed", class_name);
        return false;
    }

    PM_LOG_INFO("Page(%s) install success", class_name);
    return true;
}

/**
 * @brief  获取当前加载动画类型
 * @param  manager: 页面管理器指针
 * @retval 动画类型
 */
pm_load_anim_t pm_get_current_load_anim_type(page_manager_t *manager) {
    if (manager == NULL) {
        return PM_LOAD_ANIM_NONE;
    }
    return (pm_load_anim_t) manager->anim_state.current.type;
}
