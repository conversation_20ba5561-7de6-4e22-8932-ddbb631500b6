//
// Created by <PERSON><PERSON> on 2025/8/28 13:49.
//

#include "pm_state.h"

#include "pm_anim.h"


/**
 * @brief  更新页面状态机
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval None
 */
void pm_state_update(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return;
    }

    switch (page->priv.state) {
        case PM_PAGE_STATE_IDLE:
            PM_LOG_INFO("Page(%s) state idle", page->name);
            break;

        case PM_PAGE_STATE_LOAD:
            page->priv.state = pm_state_load_execute(manager, page);
            pm_state_update(manager, page);
            break;

        case PM_PAGE_STATE_WILL_APPEAR:
            page->priv.state = pm_state_will_appear_execute(manager, page);
            break;

        case PM_PAGE_STATE_DID_APPEAR:
            page->priv.state = pm_state_did_appear_execute(manager, page);
            PM_LOG_INFO("Page(%s) state active", page->name);
            break;

        case PM_PAGE_STATE_ACTIVITY:
            PM_LOG_INFO("Page(%s) state active break", page->name);
            page->priv.state = PM_PAGE_STATE_WILL_DISAPPEAR;
            pm_state_update(manager, page);
            break;

        case PM_PAGE_STATE_WILL_DISAPPEAR:
            page->priv.state = pm_state_will_disappear_execute(manager, page);
            break;

        case PM_PAGE_STATE_DID_DISAPPEAR:
            page->priv.state = pm_state_did_disappear_execute(manager, page);
            if (page->priv.state == PM_PAGE_STATE_UNLOAD) {
                pm_state_update(manager, page);
            }
            break;

        case PM_PAGE_STATE_UNLOAD:
            page->priv.state = pm_state_unload_execute(manager, page);
            break;

        default:
            PM_LOG_ERROR("Page(%s) state[%d] was NOT FOUND!", page->name, page->priv.state);
            break;
    }
}

/**
 * @brief  页面加载状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_load_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state load", page->name);

    if (page->root != NULL) {
        PM_LOG_ERROR("Page(%s) root must be NULL", page->name);
    }

    /* 创建根对象 */
    lv_obj_t *root_obj = lv_obj_create(lv_screen_active());

    /* 清除滚动标志 */
    lv_obj_remove_flag(root_obj, LV_OBJ_FLAG_SCROLLABLE);
    /* 设置用户数据为页面基类指针 */
    lv_obj_set_user_data(root_obj, page);

    /* 如果有默认样式，则应用到根对象 */
    if (manager->root_default_style) {
        lv_obj_add_style(root_obj, manager->root_default_style, LV_PART_MAIN);
    }

    /* 设置根对象大小为全屏 */
    lv_obj_set_size(root_obj, LV_HOR_RES, LV_VER_RES);

    /* 初始状态隐藏页面 */
    lv_obj_add_flag(root_obj, LV_OBJ_FLAG_HIDDEN);

    page->root = root_obj;

    /* 调用用户自定义属性配置 */
    if (page->vtable && page->vtable->on_custom_attr_config) {
        page->vtable->on_custom_attr_config(page);
    }

    /* 调用页面加载回调 */
    if (page->vtable && page->vtable->on_view_load) {
        page->vtable->on_view_load(page);
    }

    /* 启用拖拽功能 */
    // pm_page_manager_root_enable_drag(manager, root_obj);

    /* 调用页面加载完成回调 */
    if (page->vtable && page->vtable->on_view_did_load) {
        page->vtable->on_view_did_load(page);
    }

    return PM_PAGE_STATE_WILL_APPEAR;
}

/**
 * @brief  页面即将显示状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_will_appear_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state will appear", page->name);

    /* 调用页面即将显示回调 */
    if (page->vtable && page->vtable->on_view_will_appear) {
        page->vtable->on_view_will_appear(page);
    }

    /* 清除隐藏标志，使页面可见 */
    lv_obj_remove_flag(page->root, LV_OBJ_FLAG_HIDDEN);

    /* 创建切换动画 */
    pm_switch_anim_create(manager, page);

    return PM_PAGE_STATE_DID_APPEAR;
}

/**
 * @brief  页面已显示状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_did_appear_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state did appear", page->name);

    /* 调用页面已显示回调 */
    if (page->vtable && page->vtable->on_view_did_appear) {
        page->vtable->on_view_did_appear(page);
    }

    return PM_PAGE_STATE_ACTIVITY;
}

/**
 * @brief  页面即将消失状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_will_disappear_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state will disappear", page->name);

    /* 调用页面即将消失回调 */
    if (page->vtable && page->vtable->on_view_will_disappear) {
        page->vtable->on_view_will_disappear(page);
    }

    /* 创建切换动画 */
    pm_switch_anim_create(manager, page);

    return PM_PAGE_STATE_DID_DISAPPEAR;
}

/**
 * @brief  页面已消失结束状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_did_disappear_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state did disappear", page->name);

    /* 添加隐藏标志，隐藏页面 */
    lv_obj_add_flag(page->root, LV_OBJ_FLAG_HIDDEN);

    /* 调用页面已消失回调 */
    if (page->vtable && page->vtable->on_view_did_disappear) {
        page->vtable->on_view_did_disappear(page);
    }

    /* 根据缓存状态决定下一步 */
    if (page->priv.is_cached) {
        PM_LOG_INFO("Page(%s) has cached", page->name);
        return PM_PAGE_STATE_WILL_APPEAR;
    } else {
        return PM_PAGE_STATE_UNLOAD;
    }
}

/**
 * @brief  页面卸载完成状态
 * @param  manager: 页面管理器指针
 * @param  page: 要更新的页面指针
 * @retval 下一个状态
 */
pm_page_state_t pm_state_unload_execute(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return PM_PAGE_STATE_IDLE;
    }

    PM_LOG_INFO("Page(%s) state unload", page->name);

    if (page->root == NULL) {
        PM_LOG_WARN("Page is already unloaded!");
        goto Exit;
    }

    /* 调用页面卸载回调 */
    if (page->vtable && page->vtable->on_view_unload) {
        page->vtable->on_view_unload(page);
    }

    /* 释放页面参数缓存 */
    if (page->priv.stash.ptr != NULL && page->priv.stash.size != 0) {
        PM_LOG_INFO("Page(%s) free stash(0x%p)[%d]", page->name, page->priv.stash.ptr, page->priv.stash.size);
        free(page->priv.stash.ptr);
        page->priv.stash.ptr = NULL;
        page->priv.stash.size = 0;
    }

    /* 在根对象动画生命周期结束后删除 */
    lv_obj_delete_async(page->root);
    page->root = NULL;
    page->priv.is_cached = false;

    /* 调用页面卸载完成回调 */
    if (page->vtable && page->vtable->on_view_did_unload) {
        page->vtable->on_view_did_unload(page);
    }
Exit:
    return PM_PAGE_STATE_IDLE;
}
