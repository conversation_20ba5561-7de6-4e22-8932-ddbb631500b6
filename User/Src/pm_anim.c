//
// Created by <PERSON><PERSON> on 2025/8/28 13:53.
//

#include "pm_anim.h"

/* 动画回调函数 */
static void pm_on_switch_anim_finish(lv_anim_t *a);

/**
 * @brief  获取页面加载动画属性
 * @param  manager: 页面管理器指针
 * @param  anim: 动画类型
 * @param  attr: 属性指针
 * @retval 获取是否成功
 */
bool pm_page_manager_get_load_anim_attr(page_manager_t *manager, uint8_t anim, pm_load_anim_attr_t *attr) {
    if (manager == NULL || attr == NULL) {
        return false;
    }

    const lv_coord_t hor = LV_HOR_RES; // 水平分辨率
    const lv_coord_t ver = LV_VER_RES; // 垂直分辨率

    switch (anim) {
        case PM_LOAD_ANIM_OVER_LEFT: // 从左侧覆盖动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_HOR;
            attr->setter = (pm_anim_setter_t) lv_obj_set_x;
            attr->getter = (pm_anim_getter_t) lv_obj_get_x;

            attr->push.enter.start = hor; // 推入时进入页面从右侧开始
            attr->push.enter.end = 0;     // 推入时进入页面到中心位置结束
            attr->push.exit.start = 0;    // 推入时退出页面保持在中心
            attr->push.exit.end = 0;      // 推入时退出页面保持在中心

            attr->pop.enter.start = 0; // 弹出时进入页面从中心开始
            attr->pop.enter.end = 0;   // 弹出时进入页面保持在中心
            attr->pop.exit.start = 0;  // 弹出时退出页面从中心开始
            attr->pop.exit.end = hor;  // 弹出时退出页面向右侧移动
            break;

        case PM_LOAD_ANIM_OVER_RIGHT: // 从右侧覆盖动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_HOR;
            attr->setter = (pm_anim_setter_t) lv_obj_set_x;
            attr->getter = (pm_anim_getter_t) lv_obj_get_x;

            attr->push.enter.start = -hor;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = 0;

            attr->pop.enter.start = 0;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = -hor;
            break;

        case PM_LOAD_ANIM_OVER_TOP: // 从顶部覆盖动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_VER;
            attr->setter = (pm_anim_setter_t) lv_obj_set_y;
            attr->getter = (pm_anim_getter_t) lv_obj_get_y;

            attr->push.enter.start = ver;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = 0;

            attr->pop.enter.start = 0;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = ver;
            break;

        case PM_LOAD_ANIM_OVER_BOTTOM: // 从底部覆盖动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_VER;
            attr->setter = (pm_anim_setter_t) lv_obj_set_y;
            attr->getter = (pm_anim_getter_t) lv_obj_get_y;

            attr->push.enter.start = -ver;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = 0;

            attr->pop.enter.start = 0;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = -ver;
            break;

        case PM_LOAD_ANIM_MOVE_LEFT: // 向左推动动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_HOR;
            attr->setter = (pm_anim_setter_t) lv_obj_set_x;
            attr->getter = (pm_anim_getter_t) lv_obj_get_x;

            attr->push.enter.start = hor;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = -hor;

            attr->pop.enter.start = -hor;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = hor;
            break;

        case PM_LOAD_ANIM_MOVE_RIGHT: // 向右推动动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_HOR;
            attr->setter = (pm_anim_setter_t) lv_obj_set_x;
            attr->getter = (pm_anim_getter_t) lv_obj_get_x;

            attr->push.enter.start = -hor;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = hor;

            attr->pop.enter.start = hor;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = -hor;
            break;

        case PM_LOAD_ANIM_MOVE_TOP: // 向上推动动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_VER;
            attr->setter = (pm_anim_setter_t) lv_obj_set_y;
            attr->getter = (pm_anim_getter_t) lv_obj_get_y;

            attr->push.enter.start = ver;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = -ver;

            attr->pop.enter.start = -ver;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = ver;
            break;

        case PM_LOAD_ANIM_MOVE_BOTTOM: // 向下推动动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_VER;
            attr->setter = (pm_anim_setter_t) lv_obj_set_y;
            attr->getter = (pm_anim_getter_t) lv_obj_get_y;

            attr->push.enter.start = -ver;
            attr->push.enter.end = 0;
            attr->push.exit.start = 0;
            attr->push.exit.end = ver;

            attr->pop.enter.start = ver;
            attr->pop.enter.end = 0;
            attr->pop.exit.start = 0;
            attr->pop.exit.end = -ver;
            break;

        case PM_LOAD_ANIM_FADE_ON: // 淡入淡出动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_NONE;
            attr->setter = (pm_anim_setter_t) lv_obj_set_style_opa;
            attr->getter = (pm_anim_getter_t) lv_obj_get_style_opa;

            attr->push.enter.start = LV_OPA_TRANSP;
            attr->push.enter.end = LV_OPA_COVER;
            attr->push.exit.start = LV_OPA_COVER;
            attr->push.exit.end = LV_OPA_TRANSP;

            attr->pop.enter.start = LV_OPA_TRANSP;
            attr->pop.enter.end = LV_OPA_COVER;
            attr->pop.exit.start = LV_OPA_COVER;
            attr->pop.exit.end = LV_OPA_TRANSP;
            break;

        case PM_LOAD_ANIM_NONE: // 无动画
            attr->drag_dir = PM_ROOT_DRAG_DIR_NONE;
            attr->setter = NULL;
            attr->getter = NULL;

            memset(&attr->push, 0, sizeof(attr->push));
            memset(&attr->pop, 0, sizeof(attr->pop));
            break;

        default:
            PM_LOG_ERROR("Unknown animation type: %d", anim);
            return false;
    }

    return true;
}

/**
 * @brief  获取当前加载动画属性
 * @param  manager: 页面管理器指针
 * @param  attr: 属性指针
 * @retval 获取是否成功
 */
bool pm_get_current_load_anim_attr(page_manager_t *manager, pm_load_anim_attr_t *attr) {
    if (manager == NULL || attr == NULL) {
        return false;
    }

    return pm_page_manager_get_load_anim_attr(manager,
                                              pm_get_current_load_anim_type(manager), attr);
}

/**
 * @brief  设置动画默认参数
 * @param  manager: 页面管理器指针
 * @param  a: 动画指针
 * @retval None
 */
void pm_anim_default_init(page_manager_t *manager, lv_anim_t *a) {
    if (manager == NULL || a == NULL) {
        return;
    }

    lv_anim_init(a);

    uint32_t time = (pm_get_current_load_anim_type(manager) == PM_LOAD_ANIM_NONE)
                        ? 0
                        : manager->anim_state.current.time;
    lv_anim_set_time(a, time);
    lv_anim_set_path_cb(a, manager->anim_state.current.path);
}

/**
 * @brief  更新页面切换动画类型
 * @param  manager: 页面管理器指针
 * @param  page: 页面指针
 * @retval None
 */
void pm_switch_anim_type_update(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return;
    }

    /* 如果页面有自定义动画属性，则使用页面的动画属性 */
    if (page->priv.anim.attr.type != PM_LOAD_ANIM_GLOBAL) {
        manager->anim_state.current = page->priv.anim.attr;
        PM_LOG_INFO("Page(%s) use custom anim type: %d", page->name, page->priv.anim.attr.type);
    } else {
        /* 否则使用全局动画属性 */
        manager->anim_state.current = manager->anim_state.global;
        PM_LOG_INFO("Page(%s) use global anim type: %d", page->name, manager->anim_state.global.type);
    }

    /* 设置页面动画属性 */
    page->priv.anim.attr = manager->anim_state.current;
    page->priv.anim.is_enter = manager->anim_state.is_entering;
}

/**
 * @brief  创建页面切换动画
 * @param  manager: 页面管理器指针
 * @param  page: 指向动画页面的指针
 * @retval None
 */
void pm_switch_anim_create(page_manager_t *manager, pm_page_base_t *page) {
    if (manager == NULL || page == NULL) {
        return;
    }

    pm_load_anim_attr_t anim_attr;
    if (!pm_get_current_load_anim_attr(manager, &anim_attr)) {
        PM_LOG_ERROR("Failed to get animation attributes");
        return;
    }

    /* 如果没有动画设置器，直接完成动画 */
    if (anim_attr.setter == NULL) {
        pm_on_switch_anim_finish(NULL);
        return;
    }

    lv_anim_t a;
    pm_anim_default_init(manager, &a);
    lv_anim_set_user_data(&a, page);
    lv_anim_set_var(&a, page->root);
    lv_anim_set_completed_cb(&a, pm_on_switch_anim_finish);
    lv_anim_set_exec_cb(&a, (lv_anim_exec_xcb_t) anim_attr.setter);

    int32_t start = 0;

    if (anim_attr.getter) {
        start = anim_attr.getter(page->root);
    }

    if (manager->anim_state.is_entering) {
        if (page->priv.anim.is_enter) {
            lv_anim_set_values(&a, anim_attr.push.enter.start, anim_attr.push.enter.end);
        } else { /* 退出 */
            lv_anim_set_values(&a, start, anim_attr.push.exit.end);
        }
    } else { /* 弹出动画 */
        if (page->priv.anim.is_enter) {
            lv_anim_set_values(&a, anim_attr.pop.enter.start, anim_attr.pop.enter.end);
        } else { /* 退出 */
            lv_anim_set_values(&a, start, anim_attr.pop.exit.end);
        }
    }

    /* 设置动画忙状态 */
    page->priv.anim.is_busy = true;
    manager->anim_state.is_busy = true;

    lv_anim_start(&a);

    PM_LOG_INFO("Page(%s) animation started", page->name);
}

/**
 * @brief  页面切换动画完成回调
 * @param  a: 动画指针
 * @retval None
 */
static void pm_on_switch_anim_finish(lv_anim_t *a) {
    pm_page_base_t *page = NULL;
    page_manager_t *manager = NULL;

    if (a != NULL) {
        page = (pm_page_base_t *) lv_anim_get_user_data(a);
        if (page != NULL) {
            manager = page->manager;
            page->priv.anim.is_busy = false;
            PM_LOG_INFO("Page(%s) animation finished", page->name);
        }
    }

    if (manager != NULL) {
        manager->anim_state.is_busy = false;
        manager->anim_state.is_switch_req = false;
        PM_LOG_INFO("Page manager animation finished");
    }
}
