//
// Created by <PERSON><PERSON> on 2025/8/27 19:53.
//

#include "pm_base.h"

/* 默认虚函数表 */
static pm_page_vtable_t default_vtable = {
        .on_custom_attr_config = NULL,
        .on_view_load = NULL,
        .on_view_did_load = NULL,
        .on_view_will_appear = NULL,
        .on_view_did_appear = NULL,
        .on_view_will_disappear = NULL,
        .on_view_did_disappear = NULL,
        .on_view_unload = NULL,
        .on_view_did_unload = NULL
};

/**
 * @brief  创建页面基类
 * @retval 页面基类指针，失败返回NULL
 */
pm_page_base_t *pm_page_base_create() {
    pm_page_base_t *page = (pm_page_base_t *) malloc(sizeof(pm_page_base_t));
    if (page == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for page base");
        return NULL;
    }

    /* 初始化成员变量 */
    memset(page, 0, sizeof(pm_page_base_t));

    /* 设置默认虚函数表 */
    page->vtable = &default_vtable;

    /* 初始化私有数据 */
    page->priv.state = PM_PAGE_STATE_IDLE;
    // page->priv.req_enable_cache = false;
    // page->priv.req_disable_auto_cache = false;
    // page->priv.is_disable_auto_cache = false;
    // page->priv.is_cached = false;
    // page->priv.stash.ptr = NULL;
    // page->priv.stash.size = 0;
    page->priv.anim.is_enter = false;
    page->priv.anim.is_busy = false;
    page->priv.anim.attr.type = PM_LOAD_ANIM_GLOBAL;
    page->priv.anim.attr.time = PM_ANIM_TIME_DEFAULT;
    page->priv.anim.attr.path = PM_ANIM_PATH_DEFAULT;

    PM_LOG_INFO("Page base created");
    return page;
}

/**
 * @brief  销毁页面基类
 * @param  page: 页面基类指针
 * @retval None
 */
void pm_page_base_destroy(pm_page_base_t *page) {
    if (page == NULL) {
        return;
    }

    /* 释放stash数据 */
    // if (page->priv.stash.ptr != NULL) {
    // free(page->priv.stash.ptr);
    // page->priv.stash.ptr = NULL;
    // page->priv.stash.size = 0;
    // }

    /* 如果根对象存在，删除它 */
    if (page->root != NULL) {
        lv_obj_delete_async(page->root);
        page->root = NULL;
    }

    free(page);
    PM_LOG_INFO("Page base destroyed");
}


/**
 * @brief  设置页面虚函数表
 * @param  page: 页面基类指针
 * @param  vtable: 虚函数表指针
 * @retval None
 */
void pm_page_base_set_vtable(pm_page_base_t *page, pm_page_vtable_t *vtable) {
    if (page == NULL || vtable == NULL) {
        return;
    }

    page->vtable = vtable;
    PM_LOG_INFO("Page(%s) vtable set", page->name);
}

/**
 * @brief  设置自定义加载动画类型
 * @param  page: 页面基类指针
 * @param  anim_type: 动画类型
 * @param  time: 动画时间
 * @param  path: 动画路径
 * @retval None
 */
void pm_page_base_set_custom_load_anim_type(pm_page_base_t *page, uint8_t anim_type,
                                            uint16_t time, lv_anim_path_cb_t path) {
    if (page == NULL) {
        return;
    }

    PM_LOG_INFO("Page(%s) set_custom_load_anim_type = %d", page->name, anim_type);

    page->priv.anim.attr.type = anim_type;
    page->priv.anim.attr.time = time;
    page->priv.anim.attr.path = path;
}
