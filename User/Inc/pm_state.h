//
// Created by <PERSON><PERSON> on 2025/8/28 13:49.
//

#ifndef LVPAGEHUB_PM_STATE_H
#define LVPAGEHUB_PM_STATE_H
#include "page_manager.h"

void pm_state_update(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_load_execute(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_will_appear_execute(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_did_appear_execute(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_will_disappear_execute(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_did_disappear_execute(page_manager_t *manager, pm_page_base_t *page);

pm_page_state_t pm_state_unload_execute(page_manager_t *manager, pm_page_base_t *page);
#endif //LVPAGEHUB_PM_STATE_H
