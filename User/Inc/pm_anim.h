//
// Created by <PERSON><PERSON> on 2025/8/28 13:53.
//

#ifndef LVPAGEHUB_PM_ANIM_H
#define LVPAGEHUB_PM_ANIM_H

#include "page_manager.h"

void pm_switch_anim_type_update(page_manager_t *manager, pm_page_base_t *page);

void pm_switch_anim_create(page_manager_t *manager, pm_page_base_t *page);

bool pm_get_load_anim_attr(page_manager_t *manager, uint8_t anim, pm_load_anim_attr_t *attr);

bool pm_get_current_load_anim_attr(page_manager_t *manager, pm_load_anim_attr_t *attr);

void pm_anim_default_init(page_manager_t *manager, lv_anim_t *a);

#endif //LVPAGEHUB_PM_ANIM_H
