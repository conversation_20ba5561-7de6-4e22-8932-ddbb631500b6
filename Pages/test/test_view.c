//
// Created by <PERSON><PERSON> on 2025/8/27 19:21.
//

#include "test_view.h"


/* ========== 表盘视图实现 ========== */

/**
 * @brief 创建表盘视图对象
 * @retval 视图对象指针，失败返回NULL
 */
test_view_t *test_view_create() {
    test_view_t *view = (test_view_t *) malloc(sizeof(test_view_t));
    if (view == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for test view");
        return NULL;
    }

    /* 初始化成员变量 */
    memset(view, 0, sizeof(test_view_t));

    PM_LOG_INFO("test view created");
    return view;
}

/**
 * @brief 销毁表盘视图对象
 * @param view 视图对象指针
 */
void test_view_destroy(test_view_t *view) {
    if (view == NULL) {
        return;
    }

    /* 删除UI */
    test_view_delete_ui(view);

    free(view);
    PM_LOG_INFO("test view destroyed");
}

/**
 * @brief 删除表盘视图UI
 * @param view 视图对象指针
 */
void test_view_delete_ui(test_view_t *view) {
    if (view == NULL) {
        return;
    }

    /* 删除动画时间线 */
    if (view->ui.anim_timeline) {
        lv_anim_timeline_delete(view->ui.anim_timeline);
        view->ui.anim_timeline = NULL;
    }

    /* UI对象会随着根对象一起被删除，这里只需要清空指针 */
    memset(&view->ui, 0, sizeof(test_view_ui_t));

    PM_LOG_INFO("test view UI deleted");
}

/**
 * @brief 创建表盘视图UI
 * @param view 视图对象指针
 * @param root 根对象
 */
void test_view_create_ui(test_view_t *view, lv_obj_t *root) {
    if (view == NULL || root == NULL) {
        return;
    }

    /* 创建各个UI组件 */
    test_view_bottom_info_create(view, root);
    test_view_top_info_create(view, root);
    test_view_btn_cont_create(view, root);

    /* 创建动画时间线 */
    view->ui.anim_timeline = lv_anim_timeline_create();

    /* 定义动画宏 */
#define ANIM_DEF(start_time, obj, attr, start, end) \
{start_time, obj, LV_ANIM_EXEC(attr), start, end, 500, lv_anim_path_ease_out, true}

#define ANIM_OPA_DEF(start_time, obj) \
ANIM_DEF(start_time, obj, opa_scale, LV_OPA_TRANSP, LV_OPA_COVER)

    /* 获取目标位置 */
    lv_coord_t y_tar_top = lv_obj_get_y(view->ui.top_info.cont);
    lv_coord_t y_tar_bottom = lv_obj_get_y(view->ui.bottom_info.cont);
    lv_coord_t h_tar_btn = lv_obj_get_height(view->ui.btn_cont.btn_rec);

    /* 定义动画序列 */
    lv_anim_timeline_wrapper_t wrapper[] = {
            /* 顶部信息从上方滑入 */
            ANIM_DEF(0, view->ui.top_info.cont, y, -lv_obj_get_height(view->ui.top_info.cont), y_tar_top),

            /* 底部信息从下方滑入并淡入 */
            ANIM_DEF(200, view->ui.bottom_info.cont, y, -lv_obj_get_height(view->ui.bottom_info.cont), y_tar_bottom),
            ANIM_OPA_DEF(200, view->ui.bottom_info.cont),

            /* 按钮依次展开 */
            ANIM_DEF(500, view->ui.btn_cont.btn_map, height, 0, h_tar_btn),
            ANIM_DEF(600, view->ui.btn_cont.btn_rec, height, 0, h_tar_btn),
            ANIM_DEF(700, view->ui.btn_cont.btn_menu, height, 0, h_tar_btn),
            LV_ANIM_TIMELINE_WRAPPER_END
    };

    /* 添加动画到时间线 */
    lv_anim_timeline_add_wrapper(view->ui.anim_timeline, wrapper);

    PM_LOG_INFO("test view UI created");
}

/**
 * @brief 创建底部信息区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void test_view_bottom_info_create(test_view_t *view, lv_obj_t *par) {
    if (view == NULL || par == NULL) {
        return;
    }

    /* 创建底部容器 */
    lv_obj_t *cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, LV_HOR_RES, 85);
    lv_obj_align(cont, LV_ALIGN_BOTTOM_MID, 0, -40);

    /* 设置弹性布局 */
    lv_obj_set_flex_flow(cont, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(
            cont,
            LV_FLEX_ALIGN_SPACE_EVENLY,
            LV_FLEX_ALIGN_CENTER,
            LV_FLEX_ALIGN_CENTER
            );
    view->ui.bottom_info.cont = cont;

    /* 创建信息组 */
    const char *unit_texts[] = {"AVG", "TIME", "DIST", "CAL"};

    // for (int i = 0; i < 4; i++) {
    //     test_view_sub_info_grp_create(cont, &view->ui.bottom_info.label_info_grp[i], unit_texts[i]);
    // }

    PM_LOG_INFO("Bottom info area created");
}

/**
 * @brief 创建顶部信息区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void test_view_top_info_create(test_view_t *view, lv_obj_t *par) {
    if (view == NULL || par == NULL) {
        return;
    }

    /* 创建顶部容器 */
    lv_obj_t *cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, LV_HOR_RES, 142);

    /* 设置容器样式 */
    lv_obj_set_style_bg_opa(cont, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(cont, lv_color_hex(0x333333), 0);
    lv_obj_set_style_radius(cont, 27, 0);
    lv_obj_set_y(cont, -36);

    view->ui.top_info.cont = cont;

    /* 创建速度标签 */
    lv_obj_t *label_speed = lv_label_create(cont);
    // lv_obj_set_style_text_font(label_speed, test_get_font("bahnschrift_65"), 0);
    lv_obj_set_style_text_color(label_speed, lv_color_white(), 0);
    lv_label_set_text(label_speed, "00");
    lv_obj_align(label_speed, LV_ALIGN_TOP_MID, 0, 63);
    view->ui.top_info.label_speed = label_speed;

    /* 创建单位标签 */
    lv_obj_t *label_unit = lv_label_create(cont);
    // lv_obj_set_style_text_font(label_unit, test_get_font("bahnschrift_17"), 0);
    lv_obj_set_style_text_color(label_unit, lv_color_white(), 0);
    lv_label_set_text(label_unit, "km/h");
    lv_obj_align_to(label_unit, label_speed, LV_ALIGN_OUT_BOTTOM_MID, 0, -5);
    view->ui.top_info.label_unit = label_unit;

    PM_LOG_INFO("Top info area created");
}

/**
 * @brief 创建按钮
 * @param par 父对象
 * @param img_src 图片源
 * @param x_ofs X轴偏移
 * @retval 创建的按钮对象
 */
static lv_obj_t *test_view_btn_create(lv_obj_t *par, const void *img_src, lv_coord_t x_ofs) {
    if (par == NULL) {
        return NULL;
    }

    /* 创建按钮对象 */
    lv_obj_t *btn = lv_obj_create(par);
    lv_obj_remove_style_all(btn);
    lv_obj_set_size(btn, 58, 58);

    /* 设置按钮样式 */
    lv_obj_set_style_bg_opa(btn, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x333333), 0);
    lv_obj_set_style_bg_img_src(btn, img_src, 0);
    lv_obj_set_style_radius(btn, 29, 0);

    /* 设置按下状态样式 */
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x555555), LV_STATE_PRESSED);
    lv_obj_set_style_transform_zoom(btn, 240, LV_STATE_PRESSED);

    /* 设置焦点状态样式 */
    lv_obj_set_style_outline_width(btn, 2, LV_STATE_FOCUSED);
    lv_obj_set_style_outline_color(btn, lv_color_white(), LV_STATE_FOCUSED);
    lv_obj_set_style_outline_opa(btn, LV_OPA_50, LV_STATE_FOCUSED);

    /* 添加可点击标志 */
    lv_obj_add_flag(btn, LV_OBJ_FLAG_CLICKABLE);

    /* 设置位置 */
    lv_obj_align(btn, LV_ALIGN_CENTER, x_ofs, 0);

    return btn;
}

/**
 * @brief 创建按钮控制区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void test_view_btn_cont_create(test_view_t *view, lv_obj_t *par) {
    if (view == NULL || par == NULL) {
        return;
    }

    /* 创建按钮容器 */
    lv_obj_t *cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, 194, 58);
    lv_obj_align(cont, LV_ALIGN_CENTER, 0, 60);

    view->ui.btn_cont.cont = cont;

    /* 创建地图按钮 */
    view->ui.btn_cont.btn_map = test_view_btn_create(cont, NULL, -58);
    /* 创建录制按钮 */
    view->ui.btn_cont.btn_rec = test_view_btn_create(cont, NULL, 0);
    /* 创建菜单按钮 */
    view->ui.btn_cont.btn_menu = test_view_btn_create(cont, NULL, 58);

    PM_LOG_INFO("Button control area created");
}
