//
// Created by <PERSON><PERSON> on 2025/8/27 19:21.
//

#include "test.h"


/* 表盘虚函数表 */
static pm_page_vtable_t test_vtable = {
        .on_custom_attr_config = test_on_custom_attr_config,
        .on_view_load = test_on_view_load,
        .on_view_did_load = test_on_view_did_load,
        .on_view_will_appear = test_on_view_will_appear,
        .on_view_did_appear = test_on_view_did_appear,
        .on_view_will_disappear = test_on_view_will_disappear,
        .on_view_did_disappear = test_on_view_did_disappear,
        .on_view_unload = test_on_view_unload,
        .on_view_did_unload = test_on_view_did_unload
};

test_t *test_create() {
    test_t *test = (test_t *) malloc(sizeof(test_t));
    if (test == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for test");
        return NULL;
    }

    /* 初始化成员变量 */
    memset(test, 0, sizeof(test_t));

    /* 初始化基类 */
    pm_page_base_t *base = pm_page_base_create();
    if (base == NULL) {
        free(test);
        PM_LOG_ERROR("Failed to create page base");
        return NULL;
    }

    test->base = *base;
    free(base); // 释放临时创建的基类

    /* 设置虚函数表 */
    pm_page_base_set_vtable(&test->base, &test_vtable);

    /* 初始化表盘特有成员 */
    // test->rec_state = DIALPLATE_RECORD_STATE_READY;
    test->last_focus = NULL;
    test->timer = NULL;

    /* 创建模型和视图 */
    // test_model_t *model = test_model_create();
    test_view_t *view = test_view_create();

    // if (model == NULL || view == NULL) {
    if (view == NULL) {
        // if (model) {
        //     test_model_destroy(model);
        // }
        if (view) {
            test_view_destroy(view);
        }
        pm_page_base_destroy(&test->base);
        free(test);
        PM_LOG_ERROR("Failed to create model or view");
        return NULL;
    }

    // test->model = *model;
    test->view = *view;
    // free(model);
    free(view);

    PM_LOG_INFO("test created successfully");
    return test;
}

/* ========== 页面生命周期回调实现 ========== */

/**
 * @brief 自定义属性配置回调
 * @param self 页面基类指针
 */
void test_on_custom_attr_config(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    /* 设置无动画加载 */
    pm_page_base_set_custom_load_anim_type(self, PM_LOAD_ANIM_NONE, 0, NULL);

    PM_LOG_INFO("test custom attr config");
}

/**
 * @brief 页面加载回调
 * @param self 页面基类指针
 */
void test_on_view_load(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    test_t *test = (test_t *) self;

    /* 初始化模型 */
    // test_model_init(&test->model);

    /* 创建视图UI */
    test_view_create_ui(&test->view, self->root);

    /* 绑定事件 */
    test_attach_event(test, test->view.ui.btn_cont.btn_map);
    test_attach_event(test, test->view.ui.btn_cont.btn_rec);
    test_attach_event(test, test->view.ui.btn_cont.btn_menu);

    PM_LOG_INFO("test view loaded");
}

/**
 * @brief 页面加载完成回调
 * @param self 页面基类指针
 */
void test_on_view_did_load(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    PM_LOG_INFO("test view did load");
}

/**
 * @brief 页面即将显示回调
 * @param self 页面基类指针
 */
void test_on_view_will_appear(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    test_t *test = (test_t *) self;

    /* 等待输入设备释放 */
    lv_indev_wait_release(lv_indev_get_act());

    /* 设置焦点组 */
    lv_group_t *group = lv_group_get_default();
    if (group) {
        lv_group_set_wrap(group, false);

        lv_group_add_obj(group, test->view.ui.btn_cont.btn_map);
        lv_group_add_obj(group, test->view.ui.btn_cont.btn_rec);
        lv_group_add_obj(group, test->view.ui.btn_cont.btn_menu);

        if (test->last_focus) {
            lv_group_focus_obj(test->last_focus);
        } else {
            lv_group_focus_obj(test->view.ui.btn_cont.btn_rec);
        }
    }

    /* 设置状态栏样式为透明 */
    // test_model_set_status_bar_style(&test->model, 0); // 0 = TRANSP

    /* 更新显示 */
    // test_update(test);

    /* 启动显示动画 */
    test_view_appear_anim_start(&test->view, false);

    PM_LOG_INFO("test will appear");
}

/**
 * @brief 页面已显示回调
 * @param self 页面基类指针
 */
void test_on_view_did_appear(pm_page_base_t *self) {
    if (self == NULL) return;

    test_t *test = (test_t *) self;

    /* 创建更新定时器 */
    test->timer = lv_timer_create(test_on_timer_update, 1000, test);

    PM_LOG_INFO("test did appear");
}

/**
 * @brief 页面即将消失回调
 * @param self 页面基类指针
 */
void test_on_view_will_disappear(pm_page_base_t *self) {
    if (self == NULL) return;

    test_t *test = (test_t *) self;

    /* 保存当前焦点 */
    lv_group_t *group = lv_group_get_default();
    if (group) {
        test->last_focus = lv_group_get_focused(group);
        lv_group_remove_all_objs(group);
    }

    /* 删除定时器 */
    if (test->timer) {
        lv_timer_del(test->timer);
        test->timer = NULL;
    }

    PM_LOG_INFO("test will disappear");
}

/**
 * @brief 页面已消失回调
 * @param self 页面基类指针
 */
void test_on_view_did_disappear(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    PM_LOG_INFO("test did disappear");
}

/**
 * @brief 页面卸载回调
 * @param self 页面基类指针
 */
void test_on_view_unload(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    test_t *test = (test_t *) self;

    /* 反初始化模型 */
    // test_model_deinit(&test->model);

    /* 删除视图UI */
    test_view_delete_ui(&test->view);

    PM_LOG_INFO("test view unloaded");
}

/**
 * @brief 页面卸载完成回调
 * @param self 页面基类指针
 */
void test_on_view_did_unload(pm_page_base_t *self) {
    if (self == NULL) {
        return;
    }

    PM_LOG_INFO("test view did unload");
}

/**
 * @brief 绑定事件到对象
 * @param test 表盘对象指针
 * @param obj 要绑定事件的对象
 */
void test_attach_event(test_t *test, lv_obj_t *obj) {
    if (test == NULL || obj == NULL) return;

    lv_obj_add_event_cb(obj, test_on_event, LV_EVENT_ALL, test);
}

/**
 * @brief 事件回调函数
 * @param event 事件指针
 */
void test_on_event(lv_event_t *event) {
    if (event == NULL) return;

    test_t *test = (test_t *) lv_event_get_user_data(event);
    if (test == NULL) return;

    lv_obj_t *obj = lv_event_get_current_target(event);
    lv_event_code_t code = lv_event_get_code(event);

    /* 处理短按事件 */
    if (code == LV_EVENT_SHORT_CLICKED) {
        // test_on_btn_clicked(test, obj);
    }

    /* 处理录制按钮的特殊事件 */
    if (obj == test->view.ui.btn_cont.btn_rec) {
        if (code == LV_EVENT_SHORT_CLICKED) {
            // test_on_record(test, false);
        } else if (code == LV_EVENT_LONG_PRESSED) {
            // test_on_record(test, true);
        }
    }
}

/**
 * @brief 启动显示动画
 * @param view 视图对象指针
 * @param reverse 是否反向播放
 */
void test_view_appear_anim_start(test_view_t *view, bool reverse) {
    if (view == NULL || view->ui.anim_timeline == NULL) {
        return;
    }

    lv_anim_timeline_set_reverse(view->ui.anim_timeline, reverse);
    lv_anim_timeline_start(view->ui.anim_timeline);

    PM_LOG_INFO("test appear animation started (reverse: %s)", reverse ? "true" : "false");
}

/**
 * @brief 定时器更新回调
 * @param timer 定时器指针
 */
void test_on_timer_update(lv_timer_t *timer) {
    if (timer == NULL || timer->user_data == NULL) {
        return;
    }

    test_t *test = (test_t *) timer->user_data;
    // test_update(test);
}
