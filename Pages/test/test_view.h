//
// Created by <PERSON><PERSON> on 2025/8/27 19:21.
//

#ifndef LVPAGEHUB_TEST_VIEW_H
#define LVPAGEHUB_TEST_VIEW_H
#include "page_manager.h"

typedef struct {
    /* 顶部信息区域 */
    struct {
        lv_obj_t *cont;        // 顶部容器
        lv_obj_t *label_speed; // 速度标签
        lv_obj_t *label_unit;  // 速度单位标签
    } top_info;

    /* 底部信息区域 */
    struct {
        lv_obj_t *cont; // 底部容器
        // dialplate_sub_info_t label_info_grp[4]; // 信息组数组
    } bottom_info;

    /* 按钮控制区域 */
    struct {
        lv_obj_t *cont;     // 按钮容器
        lv_obj_t *btn_map;  // 地图按钮
        lv_obj_t *btn_rec;  // 录制按钮
        lv_obj_t *btn_menu; // 菜单按钮
    } btn_cont;

    lv_anim_timeline_t *anim_timeline; // 动画时间线
} test_view_ui_t;

/**
 * @brief 表盘视图结构
 *
 * 处理UI渲染和显示
 */
typedef struct {
    test_view_ui_t ui; // UI元素结构
    void *user_data;   // 用户数据
} test_view_t;

test_view_t *test_view_create();

void test_view_create_ui(test_view_t *view, lv_obj_t *root);

void test_view_delete_ui(test_view_t *view);

void test_view_destroy(test_view_t *view);

static void test_view_bottom_info_create(test_view_t *view, lv_obj_t *par);

static void test_view_top_info_create(test_view_t *view, lv_obj_t *par);

static void test_view_btn_cont_create(test_view_t *view, lv_obj_t *par);

#endif //LVPAGEHUB_TEST_VIEW_H
