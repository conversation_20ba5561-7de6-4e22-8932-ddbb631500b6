//
// Created by <PERSON><PERSON> on 2025/8/26 18:02.
//

#include "page_manager.h"

/**
 * @brief 页面基类虚函数表
 *
 * 该结构体定义了页面生命周期中各个阶段的回调函数指针，
 * 实现了类似面向对象编程中虚函数表的功能，允许不同的页面
 * 实现自己的生命周期处理逻辑。
 *
 * 页面生命周期顺序：
 * 1. on_custom_attr_config - 自定义属性配置
 * 2. on_view_load - 页面开始加载
 * 3. on_view_did_load - 页面加载完成
 * 4. on_view_will_appear - 页面即将显示（动画开始前）
 * 5. on_view_did_appear - 页面已显示（动画完成后）
 * 6. on_view_will_disappear - 页面即将消失（动画开始前）
 * 7. on_view_did_disappear - 页面已消失（动画完成后）
 * 8. on_view_unload - 页面开始卸载
 * 9. on_view_did_unload - 页面卸载完成
 */
typedef struct {
    /**
     * @brief 自定义属性配置回调
     * @param self 页面基类指针
     *
     * 在页面创建时调用，用于配置页面的自定义属性，如：
     * - 缓存设置
     * - 动画类型和参数
     * - 其他页面特定的配置
     *
     * 调用时机：页面安装(Install)时，在页面对象创建后立即调用
     */
    void (*on_custom_attr_config)(pm_page_base_t *self);

    /**
     * @brief 页面开始加载回调
     * @param self 页面基类指针
     *
     * 页面开始加载时调用，此时页面的根对象(root)已创建，
     * 可以在此回调中：
     * - 创建UI控件
     * - 初始化页面数据
     * - 设置事件处理器
     * - 加载资源文件
     *
     * 调用时机：页面切换时，在创建根对象后调用
     */
    void (*on_view_load)(pm_page_base_t *self);

    /**
     * @brief 页面加载完成回调
     * @param self 页面基类指针
     *
     * 页面UI创建完成后调用，此时所有UI控件都已创建完毕，
     * 可以在此回调中：
     * - 进行最终的UI调整
     * - 设置初始数据显示
     * - 执行一些需要在UI完全准备好后才能进行的操作
     *
     * 调用时机：on_view_load执行完成后立即调用
     */
    void (*on_view_did_load)(pm_page_base_t *self);

    /**
     * @brief 页面即将显示回调
     * @param self 页面基类指针
     *
     * 页面即将显示时调用（进入动画开始前），
     * 可以在此回调中：
     * - 启动定时器
     * - 开始数据更新
     * - 处理页面参数(stash数据)
     * - 设置页面显示前的状态
     *
     * 调用时机：页面切换动画开始前调用
     */
    void (*on_view_will_appear)(pm_page_base_t *self);

    /**
     * @brief 页面已显示回调
     * @param self 页面基类指针
     *
     * 页面完全显示后调用（进入动画完成后），
     * 可以在此回调中：
     * - 启动页面活动状态的功能
     * - 开始用户交互处理
     * - 执行需要在页面完全可见后才能进行的操作
     *
     * 调用时机：页面切换动画完成后调用
     */
    void (*on_view_did_appear)(pm_page_base_t *self);

    /**
     * @brief 页面即将消失回调
     * @param self 页面基类指针
     *
     * 页面即将消失时调用（退出动画开始前），
     * 可以在此回调中：
     * - 保存页面状态
     * - 暂停定时器
     * - 停止数据更新
     * - 清理临时资源
     *
     * 调用时机：页面切换动画开始前调用
     */
    void (*on_view_will_disappear)(pm_page_base_t *self);

    /**
     * @brief 页面已消失回调
     * @param self 页面基类指针
     *
     * 页面完全消失后调用（退出动画完成后），
     * 可以在此回调中：
     * - 停止所有页面活动
     * - 清理定时器
     * - 释放不需要缓存的资源
     *
     * 调用时机：页面切换动画完成后调用
     */
    void (*on_view_did_disappear)(pm_page_base_t *self);

    /**
     * @brief 页面开始卸载回调
     * @param self 页面基类指针
     *
     * 页面开始卸载时调用，此时页面即将被销毁，
     * 可以在此回调中：
     * - 保存重要数据
     * - 清理资源
     * - 断开连接
     *
     * 调用时机：页面卸载(Uninstall)时，在销毁UI前调用
     */
    void (*on_view_unload)(pm_page_base_t *self);

    /**
     * @brief 页面卸载完成回调
     * @param self 页面基类指针
     *
     * 页面卸载完成后调用，此时页面的UI已被销毁，
     * 可以在此回调中：
     * - 执行最终的清理工作
     * - 释放所有分配的内存
     * - 记录日志
     *
     * 调用时机：页面卸载完成后，在页面对象销毁前调用
     */
    void (*on_view_did_unload)(pm_page_base_t *self);
} pm_page_vtable_t;