/**
 * @file test_window_size.c
 * @brief 测试窗口大小调整功能
 */

#include <stdio.h>
#include "App.h"
#include "page_manager.h"
#include "lvgl/lvgl.h"
#include "lvgl/demos/lv_demos.h"

#ifdef _WIN32
#include <windows.h>
#include "lvgl/src/drivers/windows/lv_windows_display.h"
#endif

static const wchar_t * title = L"LVGL Window Size Test - Target Client Area: 600x480";

void print_window_info(lv_display_t * display) {
#ifdef _WIN32
    HWND hwnd = lv_windows_get_display_window_handle(display);
    if (!hwnd) {
        printf("Failed to get window handle\n");
        return;
    }
    
    RECT window_rect, client_rect;
    GetWindowRect(hwnd, &window_rect);
    GetClientRect(hwnd, &client_rect);
    
    int window_width = window_rect.right - window_rect.left;
    int window_height = window_rect.bottom - window_rect.top;
    int client_width = client_rect.right - client_rect.left;
    int client_height = client_rect.bottom - client_rect.top;
    
    printf("=== Window Size Information ===\n");
    printf("Target client size: %dx%d\n", SCREEN_CLIENT_WIDTH, SCREEN_CLIENT_HEIGHT);
    printf("Actual window size: %dx%d\n", window_width, window_height);
    printf("Actual client size: %dx%d\n", client_width, client_height);
    printf("Window decoration: %dx%d\n", 
           window_width - client_width, 
           window_height - client_height);
    printf("LVGL display resolution: %dx%d\n", 
           lv_display_get_horizontal_resolution(display),
           lv_display_get_vertical_resolution(display));
    
    if (client_width == SCREEN_CLIENT_WIDTH && client_height == SCREEN_CLIENT_HEIGHT) {
        printf("✓ SUCCESS: Client area size matches target!\n");
    } else {
        printf("✗ MISMATCH: Client area size differs from target\n");
        printf("  Difference: %+d x %+d\n", 
               client_width - SCREEN_CLIENT_WIDTH,
               client_height - SCREEN_CLIENT_HEIGHT);
    }
    printf("===============================\n\n");
#endif
}

int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR szCmdLine, int nCmdShow)
{
    /*Initialize LVGL*/
    lv_init();

    /*Initialize the HAL for LVGL*/
    printf("Creating window with size: %dx%d (includes decorations)\n", SCREEN_WIDTH, SCREEN_HEIGHT);
    lv_display_t * display = lv_windows_create_display(title, SCREEN_WIDTH, SCREEN_HEIGHT, 100, FALSE, FALSE);
    lv_windows_acquire_pointer_indev(display);
    
    printf("Before adjustment:\n");
    print_window_info(display);
    
    /*Adjust window size to get exact client area size*/
    printf("Adjusting window size for client area: %dx%d\n", SCREEN_CLIENT_WIDTH, SCREEN_CLIENT_HEIGHT);
    app_adjust_window_size(display, SCREEN_CLIENT_WIDTH, SCREEN_CLIENT_HEIGHT);
    
    printf("After adjustment:\n");
    print_window_info(display);

    /*Output prompt information to the console*/
    LV_LOG_USER("LVGL initialization completed!");
    LV_LOG_USER("Window size test completed!");

    /*Initialize user UI*/
    user_ui_init();
    
    /*Create a simple test UI*/
    lv_obj_t * label = lv_label_create(lv_scr_act());
    lv_label_set_text_fmt(label, "Window Size Test\nTarget: %dx%d\nActual Client: Check console", 
                         SCREEN_CLIENT_WIDTH, SCREEN_CLIENT_HEIGHT);
    lv_obj_center(label);
    
    while(1) {
        lv_task_handler();
        Sleep(5);  // Use Windows Sleep instead of usleep
    }
    return 0;
}
