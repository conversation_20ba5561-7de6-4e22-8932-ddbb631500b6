/**
 * @file lv_anim_timeline_wrapper_example.c
 * @brief LVGL动画时间轴包装器使用示例
 * 
 * 这个文件展示了如何使用动画时间轴包装器来创建复杂的动画序列。
 * 包装器简化了批量创建动画的过程，使代码更加清晰和易于维护。
 */

#include "lv_anim_timeline_wrapper.h"

/**
 * @brief 创建一个简单的动画序列示例
 * 
 * 这个示例展示了如何使用包装器创建多个对象的协调动画：
 * - 第一个对象从左侧滑入
 * - 第二个对象稍后从右侧滑入
 * - 第三个对象最后淡入显示
 */
void example_simple_animation_sequence(void) {
    // 假设我们有三个UI对象
    extern lv_obj_t *obj1, *obj2, *obj3;
    
    // 创建动画时间轴
    lv_anim_timeline_t *timeline = lv_anim_timeline_create();
    
    // 定义动画序列
    lv_anim_timeline_wrapper_t anim_sequence[] = {
        // 时间0ms: obj1从x=-100滑动到x=50，持续500ms，使用缓入缓出效果
        {
            .start_time = 0,
            .obj = obj1,
            .exec_cb = (lv_anim_exec_xcb_t)lv_obj_set_x,
            .start = -100,
            .end = 50,
            .duration = 500,
            .path_cb = lv_anim_path_ease_in_out,
            .early_apply = false
        },
        
        // 时间200ms: obj2从x=400滑动到x=150，持续400ms，使用线性效果
        {
            .start_time = 200,
            .obj = obj2,
            .exec_cb = (lv_anim_exec_xcb_t)lv_obj_set_x,
            .start = 400,
            .end = 150,
            .duration = 400,
            .path_cb = lv_anim_path_linear,
            .early_apply = false
        },
        
        // 时间400ms: obj3透明度从0变为255（淡入），持续300ms
        {
            .start_time = 400,
            .obj = obj3,
            .exec_cb = (lv_anim_exec_xcb_t)lv_obj_set_style_opa,
            .start = 0,
            .end = 255,
            .duration = 300,
            .path_cb = lv_anim_path_ease_out,
            .early_apply = true  // 提前应用初始透明度
        },
        
        // 数组结束标记
        LV_ANIM_TIMELINE_WRAPPER_END
    };
    
    // 将动画序列添加到时间轴
    lv_anim_timeline_add_wrapper(timeline, anim_sequence);
    
    // 开始播放动画时间轴
    lv_anim_timeline_start(timeline);
}

/**
 * @brief 创建一个复杂的UI转场动画示例
 * 
 * 这个示例展示了如何创建页面转场效果：
 * - 旧页面元素逐个淡出并向左移动
 * - 新页面元素从右侧滑入并淡入
 */
void example_page_transition_animation(void) {
    // 假设我们有旧页面和新页面的UI对象
    extern lv_obj_t *old_title, *old_content, *old_button;
    extern lv_obj_t *new_title, *new_content, *new_button;
    
    lv_anim_timeline_t *timeline = lv_anim_timeline_create();
    
    lv_anim_timeline_wrapper_t transition_sequence[] = {
        // === 旧页面淡出动画 ===
        
        // 0ms: 旧标题开始淡出
        {0, old_title, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 255, 0, 300, lv_anim_path_ease_in, false},
        
        // 0ms: 旧标题同时向左移动
        {0, old_title, (lv_anim_exec_xcb_t)lv_obj_set_x, 0, -200, 300, lv_anim_path_ease_in, false},
        
        // 100ms: 旧内容开始淡出和左移（稍有延迟）
        {100, old_content, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 255, 0, 300, lv_anim_path_ease_in, false},
        {100, old_content, (lv_anim_exec_xcb_t)lv_obj_set_x, 0, -200, 300, lv_anim_path_ease_in, false},
        
        // 200ms: 旧按钮最后淡出和左移
        {200, old_button, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 255, 0, 300, lv_anim_path_ease_in, false},
        {200, old_button, (lv_anim_exec_xcb_t)lv_obj_set_x, 0, -200, 300, lv_anim_path_ease_in, false},
        
        // === 新页面淡入动画 ===
        
        // 300ms: 新标题从右侧滑入并淡入
        {300, new_title, (lv_anim_exec_xcb_t)lv_obj_set_x, 200, 0, 400, lv_anim_path_ease_out, true},
        {300, new_title, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 0, 255, 400, lv_anim_path_ease_out, true},
        
        // 400ms: 新内容滑入并淡入
        {400, new_content, (lv_anim_exec_xcb_t)lv_obj_set_x, 200, 0, 400, lv_anim_path_ease_out, true},
        {400, new_content, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 0, 255, 400, lv_anim_path_ease_out, true},
        
        // 500ms: 新按钮最后滑入并淡入
        {500, new_button, (lv_anim_exec_xcb_t)lv_obj_set_x, 200, 0, 400, lv_anim_path_ease_out, true},
        {500, new_button, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 0, 255, 400, lv_anim_path_ease_out, true},
        
        LV_ANIM_TIMELINE_WRAPPER_END
    };
    
    lv_anim_timeline_add_wrapper(timeline, transition_sequence);
    lv_anim_timeline_start(timeline);
}

/**
 * @brief 包装器的优势说明
 * 
 * 使用动画时间轴包装器的优势：
 * 
 * 1. 代码简洁：将动画参数集中在数组中，一目了然
 * 2. 易于维护：修改动画参数只需要修改数组中的值
 * 3. 批量操作：可以一次性创建多个相关的动画
 * 4. 时间控制：精确控制每个动画的开始时间
 * 5. 复用性强：动画序列可以轻松复制和修改
 * 
 * 对比传统方式：
 * - 传统方式需要为每个动画单独调用lv_anim_init、lv_anim_set_*等函数
 * - 包装器方式只需要定义数组和调用一个函数
 * - 动画的时序关系在数组中更加清晰
 */
