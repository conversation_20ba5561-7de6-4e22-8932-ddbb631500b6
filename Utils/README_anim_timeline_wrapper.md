# LVGL动画时间轴包装器

## 概述

LVGL动画时间轴包装器是一个简化LVGL动画创建和管理的工具模块。它提供了一个更直观、更易维护的方式来创建复杂的动画序列。

## 功能特点

- **简化接口**：通过结构体数组定义动画，避免重复的函数调用
- **批量操作**：一次性创建多个相关动画
- **时间控制**：精确控制每个动画的开始时间
- **易于维护**：动画参数集中管理，修改方便
- **代码清晰**：动画序列的时序关系一目了然

## 文件结构

```
Utils/
├── lv_anim_timeline_wrapper.h          # 头文件，包含类型定义和函数声明
├── lv_anim_timeline_wrapper.c          # 实现文件
├── lv_anim_timeline_wrapper_example.c  # 使用示例
└── README_anim_timeline_wrapper.md     # 本说明文档
```

## 核心数据结构

```c
typedef struct {
    uint32_t start_time;           // 动画开始时间（毫秒）
    lv_obj_t *obj;                 // 要执行动画的对象指针
    lv_anim_exec_xcb_t exec_cb;    // 动画执行回调函数
    int32_t start;                 // 动画起始值
    int32_t end;                   // 动画结束值
    uint16_t duration;             // 动画持续时间（毫秒）
    lv_anim_path_cb_t path_cb;     // 动画路径回调函数（缓动函数）
    bool early_apply;              // 是否提前应用动画值
} lv_anim_timeline_wrapper_t;
```

## 基本用法

### 1. 包含头文件

```c
#include "lv_anim_timeline_wrapper.h"
```

### 2. 定义动画序列

```c
lv_anim_timeline_wrapper_t anim_sequence[] = {
    // 时间0ms: obj1从x=0移动到x=100，持续500ms
    {
        .start_time = 0,
        .obj = obj1,
        .exec_cb = (lv_anim_exec_xcb_t)lv_obj_set_x,
        .start = 0,
        .end = 100,
        .duration = 500,
        .path_cb = lv_anim_path_ease_in_out,
        .early_apply = false
    },
    
    // 时间200ms: obj2淡入显示
    {
        .start_time = 200,
        .obj = obj2,
        .exec_cb = (lv_anim_exec_xcb_t)lv_obj_set_style_opa,
        .start = 0,
        .end = 255,
        .duration = 300,
        .path_cb = lv_anim_path_linear,
        .early_apply = true
    },
    
    // 数组结束标记
    LV_ANIM_TIMELINE_WRAPPER_END
};
```

### 3. 创建时间轴并添加动画

```c
lv_anim_timeline_t *timeline = lv_anim_timeline_create();
lv_anim_timeline_add_wrapper(timeline, anim_sequence);
lv_anim_timeline_start(timeline);
```

## 常用动画类型

### 位置动画
```c
{time, obj, (lv_anim_exec_xcb_t)lv_obj_set_x, start_x, end_x, duration, path, false}
{time, obj, (lv_anim_exec_xcb_t)lv_obj_set_y, start_y, end_y, duration, path, false}
```

### 透明度动画
```c
{time, obj, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 0, 255, duration, path, true}
```

### 尺寸动画
```c
{time, obj, (lv_anim_exec_xcb_t)lv_obj_set_width, start_w, end_w, duration, path, false}
{time, obj, (lv_anim_exec_xcb_t)lv_obj_set_height, start_h, end_h, duration, path, false}
```

## 常用缓动函数

- `lv_anim_path_linear` - 线性动画
- `lv_anim_path_ease_in` - 缓入
- `lv_anim_path_ease_out` - 缓出
- `lv_anim_path_ease_in_out` - 缓入缓出
- `lv_anim_path_overshoot` - 超调效果
- `lv_anim_path_bounce` - 弹跳效果

## 注意事项

1. **数组结束**：动画数组必须以`LV_ANIM_TIMELINE_WRAPPER_END`结尾
2. **对象生命周期**：确保动画执行期间对象仍然有效
3. **early_apply**：对于透明度等需要初始状态的动画，建议设置为true
4. **时间单位**：所有时间参数都以毫秒为单位
5. **内存管理**：时间轴使用完毕后记得调用`lv_anim_timeline_del()`释放资源

## 优势对比

### 传统方式
```c
// 需要为每个动画单独设置
lv_anim_t a1;
lv_anim_init(&a1);
lv_anim_set_var(&a1, obj1);
lv_anim_set_values(&a1, 0, 100);
lv_anim_set_exec_cb(&a1, lv_obj_set_x);
lv_anim_set_time(&a1, 500);
lv_anim_set_path_cb(&a1, lv_anim_path_ease_in_out);
lv_anim_timeline_add(timeline, 0, &a1);

lv_anim_t a2;
lv_anim_init(&a2);
// ... 重复设置过程
```

### 包装器方式
```c
// 简洁的数组定义
lv_anim_timeline_wrapper_t anims[] = {
    {0, obj1, (lv_anim_exec_xcb_t)lv_obj_set_x, 0, 100, 500, lv_anim_path_ease_in_out, false},
    {200, obj2, (lv_anim_exec_xcb_t)lv_obj_set_style_opa, 0, 255, 300, lv_anim_path_linear, true},
    LV_ANIM_TIMELINE_WRAPPER_END
};
lv_anim_timeline_add_wrapper(timeline, anims);
```

包装器方式显著减少了代码量，提高了可读性和可维护性。
