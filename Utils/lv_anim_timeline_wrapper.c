/**
 * @file lv_anim_timeline_wrapper.c
 * @brief LVGL动画时间轴包装器实现
 *
 * 这个文件实现了动画时间轴包装器的功能，提供了一个简化的接口
 * 来批量创建和管理LVGL动画。
 */

#include "lv_anim_timeline_wrapper.h"

/*********************
 *      全局函数实现
 *********************/

/**
 * @brief 将包装器数组中的动画添加到时间轴中
 *
 * 这个函数遍历包装器数组，为每个有效的包装器元素创建一个动画对象，
 * 并将其添加到指定的时间轴中。函数会一直遍历直到遇到obj为NULL的元素。
 *
 * @param at      目标动画时间轴对象
 * @param wrapper 动画包装器数组，必须以LV_ANIM_TIMELINE_WRAPPER_END结尾
 */
void lv_anim_timeline_add_wrapper(lv_anim_timeline_t *at, const lv_anim_timeline_wrapper_t *wrapper) {
    // 遍历包装器数组，直到遇到obj为NULL的结束标记
    for (uint32_t i = 0; wrapper[i].obj != NULL; i++) {
        // 获取当前包装器元素的指针
        const lv_anim_timeline_wrapper_t *atw = &wrapper[i];

        // 创建并初始化动画对象
        lv_anim_t a;
        lv_anim_init(&a);                              // 初始化动画结构体
        lv_anim_set_var(&a, atw->obj);                 // 设置动画目标对象
        lv_anim_set_values(&a, atw->start, atw->end);  // 设置动画起始值和结束值
        lv_anim_set_exec_cb(&a, atw->exec_cb);         // 设置动画执行回调函数
        lv_anim_set_time(&a, atw->duration);           // 设置动画持续时间
        lv_anim_set_path_cb(&a, atw->path_cb);         // 设置动画路径（缓动函数）
        lv_anim_set_early_apply(&a, atw->early_apply); // 设置是否提前应用动画值

        // 将配置好的动画添加到时间轴的指定时间点
        lv_anim_timeline_add(at, atw->start_time, &a);
    }
}
