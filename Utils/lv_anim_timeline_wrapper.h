/**
 * @file lv_anim_timeline_wrapper.h
 * @brief LVGL动画时间轴包装器
 *
 * 这个模块提供了一个简化的接口来创建和管理LVGL动画时间轴。
 * 通过使用包装器结构体，可以更方便地批量添加动画到时间轴中。
 */

#ifndef LV_ANIM_TIMELINE_WRAPPER_H
#define LV_ANIM_TIMELINE_WRAPPER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

/*********************
 *      类型定义
 *********************/

/**
 * @brief 动画时间轴包装器数据结构
 *
 * 这个结构体包含了创建一个动画所需的所有参数，
 * 使得批量创建动画变得更加简单和直观。
 */
typedef struct {
    uint32_t start_time;           /**< 动画开始时间（毫秒） */
    lv_obj_t *obj;                 /**< 要执行动画的对象指针 */
    lv_anim_exec_xcb_t exec_cb;    /**< 动画执行回调函数 */
    int32_t start;                 /**< 动画起始值 */
    int32_t end;                   /**< 动画结束值 */
    uint16_t duration;             /**< 动画持续时间（毫秒） */
    lv_anim_path_cb_t path_cb;     /**< 动画路径回调函数（缓动函数） */
    bool early_apply;              /**< 是否提前应用动画值 */
} lv_anim_timeline_wrapper_t;

/*********************
 *      全局函数声明
 *********************/

/**
 * @brief 将包装器数组中的动画添加到时间轴中
 *
 * 这个函数遍历包装器数组，为每个包装器创建对应的动画，
 * 并将其添加到指定的时间轴中。数组必须以LV_ANIM_TIMELINE_WRAPPER_END结尾。
 *
 * @param at      目标动画时间轴对象
 * @param wrapper 动画包装器数组，必须以LV_ANIM_TIMELINE_WRAPPER_END结尾
 *
 * @note 包装器数组中的每个元素都会被转换为一个lv_anim_t对象
 *       并添加到时间轴的指定时间点
 *
 * 使用示例：
 * @code
 * lv_anim_timeline_wrapper_t anim_array[] = {
 *     {0, obj1, lv_obj_set_x, 0, 100, 500, lv_anim_path_ease_in_out, false},
 *     {200, obj2, lv_obj_set_y, 50, 150, 300, lv_anim_path_linear, false},
 *     LV_ANIM_TIMELINE_WRAPPER_END
 * };
 * lv_anim_timeline_add_wrapper(timeline, anim_array);
 * @endcode
 */
void lv_anim_timeline_add_wrapper(lv_anim_timeline_t *at, const lv_anim_timeline_wrapper_t *wrapper);

/*********************
 *      宏定义
 *********************/

/**
 * @brief 动画包装器数组结束标记
 *
 * 用于标记动画包装器数组的结束。数组的最后一个元素必须是这个宏。
 * obj字段为NULL表示数组结束。
 */
#define LV_ANIM_TIMELINE_WRAPPER_END {0, NULL}

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_ANIM_TIMELINE_WRAPPER_H*/
